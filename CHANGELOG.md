# 1.0.0 (2025-07-03)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **planning:** reorganize development plans and align UI revamp features ([ad4a670](https://github.com/arpieper/navsync/commit/ad4a6708acff1f9b518654b5e61295d5c7c57a39))
- **security:** implement comprehensive session timeout and Plaid token encryption ([2fcdfbf](https://github.com/arpieper/navsync/commit/2fcdfbf3297819aed41df14c639345ebab744fa4))
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-07-03)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **planning:** reorganize development plans and align UI revamp features ([ad4a670](https://github.com/arpieper/navsync/commit/ad4a6708acff1f9b518654b5e61295d5c7c57a39))
- **security:** implement comprehensive session timeout and Plaid token encryption ([2fcdfbf](https://github.com/arpieper/navsync/commit/2fcdfbf3297819aed41df14c639345ebab744fa4))
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-07-02)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **planning:** reorganize development plans and align UI revamp features ([ad4a670](https://github.com/arpieper/navsync/commit/ad4a6708acff1f9b518654b5e61295d5c7c57a39))
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-07-01)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **planning:** reorganize development plans and align UI revamp features ([ad4a670](https://github.com/arpieper/navsync/commit/ad4a6708acff1f9b518654b5e61295d5c7c57a39))
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-06-30)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-06-30)

### Bug Fixes

- **deps:** Downgrade Babel packages to resolve ESM compatibility issue ([d270689](https://github.com/arpieper/navsync/commit/d270689020bd9d6c0b57dad46232da21b12e51dc))
- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-06-30)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)
- **transactions:** implement responsive and enhanced transaction view ([1767a48](https://github.com/arpieper/navsync/commit/1767a48c1dda74671509a2ade8588ab8b9a4224c))

# 1.0.0 (2025-06-29)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)

# 1.0.0 (2025-06-17)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)

# 1.0.0 (2025-06-17)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([3eb5d93](https://github.com/arpieper/navsync/commit/3eb5d934db4cf612dbcedc6beec8654158d0954f))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- complete Jest 30 migration with test fixes ([03b6bbb](https://github.com/arpieper/navsync/commit/03b6bbbc27ce957884a0083487275572b12369b5))
- **core:** Implement and test categorization system ([a841cb6](https://github.com/arpieper/navsync/commit/a841cb62666f392973fd7bc317b5d30e4aaec24c))
- **plaid:** implement transaction sync and management UI ([a492c16](https://github.com/arpieper/navsync/commit/a492c16da9b6884b26bc7fb8a4a9b557e26e317c)), closes [#2](https://github.com/arpieper/navsync/issues/2)

# [1.1.0](https://github.com/arpieper/navsync/compare/v1.0.0...v1.1.0) (2025-06-15)

### Features

- complete Jest 30 migration with test fixes ([3cbbd72](https://github.com/arpieper/navsync/commit/3cbbd721d7fff7aa4eae2dc1f037e25524955ce1))

# 1.0.0 (2025-06-15)

### Bug Fixes

- **plaid:** Resolve integration and user creation failures ([c380628](https://github.com/arpieper/navsync/commit/c3806281f5550fc90f8735220e85845531003ffd))
- Updated pre-commit ([8947042](https://github.com/arpieper/navsync/commit/8947042518c208d3f191771a332e7148dd053dcc))

### Features

- **core:** Implement and test categorization system ([4ba6715](https://github.com/arpieper/navsync/commit/4ba6715b5ec5479efd549efdfc95caf9b0b979a2))
- **plaid:** implement transaction sync and management UI ([e9df6c8](https://github.com/arpieper/navsync/commit/e9df6c83d02b6a0d77d22a8450b0ad8a7ebfb814)), closes [#2](https://github.com/arpieper/navsync/issues/2)

# Changelog

All notable changes to this project will be documented in this file.

This project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html) and uses [Conventional Commits](https://conventionalcommits.org/) for automated versioning.

## [Unreleased]

### Added

- Initial project setup with semantic-release automation
