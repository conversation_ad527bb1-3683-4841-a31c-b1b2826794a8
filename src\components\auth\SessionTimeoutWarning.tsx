'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

interface SessionTimeoutWarningProps {
  show: boolean;
  remainingSeconds: number;
  onExtendSession: () => void;
  onLogout: () => void;
}

export function SessionTimeoutWarning({
  show,
  remainingSeconds,
  onExtendSession,
  onLogout,
}: SessionTimeoutWarningProps) {
  const [seconds, setSeconds] = useState(remainingSeconds);

  useEffect(() => {
    setSeconds(remainingSeconds);
  }, [remainingSeconds]);

  // Handle automatic logout when countdown reaches 0
  useEffect(() => {
    if (seconds <= 0 && show) {
      onLogout();
    }
  }, [seconds, show, onLogout]);

  useEffect(() => {
    if (!show) return;

    const interval = setInterval(() => {
      setSeconds((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(interval);
  }, [show]);

  const formatTime = (totalSeconds: number) => {
    const minutes = Math.floor(totalSeconds / 60);
    const secs = totalSeconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  if (!show) return null;

  return (
    <div className='fixed inset-0 z-50 flex items-center justify-center'>
      {/* Backdrop */}
      <div className='fixed inset-0 bg-black/50' />

      {/* Modal */}
      <div className='relative bg-white rounded-lg shadow-lg p-6 max-w-md mx-4 z-50'>
        <div className='text-center'>
          <h2 className='text-lg font-semibold text-gray-900 mb-2 flex items-center justify-center gap-2'>
            ⚠️ Session Expiring Soon
          </h2>
          <p className='text-gray-600 mb-6'>
            Your session will expire due to inactivity in{' '}
            <span className='font-bold text-red-600'>{formatTime(seconds)}</span>. Would you like to
            extend your session?
          </p>

          <div className='flex flex-col sm:flex-row gap-3 justify-center'>
            <Button variant='outline' onClick={onLogout} className='w-full sm:w-auto'>
              Logout Now
            </Button>
            <Button onClick={onExtendSession} className='w-full sm:w-auto'>
              Stay Logged In
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
