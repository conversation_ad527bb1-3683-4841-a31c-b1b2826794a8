'use client';
import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useInactivityTimeout } from '@/hooks/useInactivityTimeout';
import { SessionTimeoutWarning } from './SessionTimeoutWarning';

const supabase = createSupabaseBrowserClient();
import type { Session, User, AuthChangeEvent } from '@supabase/supabase-js';

// Define the shape of the AuthContext
interface AuthContextType {
  session: Session | null;
  user: User | null;
  isLoading: boolean;
}

// Create the context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider props
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Supabase client is now created at the module top-level for efficiency.
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [showWarning, setShowWarning] = useState(false);

  // Derive user from session
  const user = session?.user ?? null;

  // Session timeout management (only active when user is logged in)
  const { resetTimeout, getRemainingTime } = useInactivityTimeout({
    timeoutMinutes: 30, // 30 minutes inactivity timeout
    warningMinutes: 5, // Show warning 5 minutes before timeout
    onWarning: () => {
      if (user) setShowWarning(true);
    },
    onTimeout: () => {
      setShowWarning(false);
    },
  });

  const handleExtendSession = () => {
    setShowWarning(false);
    resetTimeout();
  };

  const handleLogout = async () => {
    setShowWarning(false);
    await supabase.auth.signOut();
  };

  useEffect(() => {
    let isMounted = true;
    // Get initial session
    supabase.auth
      .getSession()
      .then(({ data }) => {
        if (isMounted) {
          setSession(data.session);
          setIsLoading(false);
        }
      })
      .catch(() => {
        if (isMounted) setIsLoading(false);
      });

    // Subscribe to auth state changes
    const { data: listener } = supabase.auth.onAuthStateChange(
      (_event: AuthChangeEvent, newSession: Session | null) => {
        setSession(newSession);
      }
    );

    // Cleanup on unmount
    return () => {
      isMounted = false;
      listener.subscription.unsubscribe();
    };
  }, []);

  const value: AuthContextType = {
    session,
    user,
    isLoading,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
      {user && (
        <SessionTimeoutWarning
          show={showWarning}
          remainingSeconds={Math.floor(getRemainingTime() / 1000)}
          onExtendSession={handleExtendSession}
          onLogout={handleLogout}
        />
      )}
    </AuthContext.Provider>
  );
};

// Custom hook for consuming the context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
