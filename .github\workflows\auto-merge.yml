name: Auto-merge Dependabot PRs

on:
  pull_request:
    types: [opened, reopened, ready_for_review]

permissions:
  contents: write
  pull-requests: write

jobs:
  auto-merge:
    if: github.actor == 'dependabot[bot]' && github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check if PR is for patch or minor update
        id: check-update-type
        run: |
          PR_TITLE="${{ github.event.pull_request.title }}"
          echo "PR Title: $PR_TITLE"
          
          # Check if it's a patch or minor update (not major)
          if [[ "$PR_TITLE" =~ bump.*from.*[0-9]+\.[0-9]+\.[0-9]+.*to.*[0-9]+\.[0-9]+\.[0-9]+ ]]; then
            # Extract version numbers
            OLD_VERSION=$(echo "$PR_TITLE" | grep -oE 'from [0-9]+\.[0-9]+\.[0-9]+' | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            NEW_VERSION=$(echo "$PR_TITLE" | grep -oE 'to [0-9]+\.[0-9]+\.[0-9]+' | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
            
            OLD_MAJOR=$(echo "$OLD_VERSION" | cut -d. -f1)
            NEW_MAJOR=$(echo "$NEW_VERSION" | cut -d. -f1)
            
            echo "Old version: $OLD_VERSION (major: $OLD_MAJOR)"
            echo "New version: $NEW_VERSION (major: $NEW_MAJOR)"
            
            if [[ "$OLD_MAJOR" == "$NEW_MAJOR" ]]; then
              echo "safe_to_merge=true" >> $GITHUB_OUTPUT
              echo "This is a patch/minor update - safe to auto-merge"
            else
              echo "safe_to_merge=false" >> $GITHUB_OUTPUT
              echo "This is a major version update - requires manual review"
            fi
          else
            echo "safe_to_merge=false" >> $GITHUB_OUTPUT
            echo "Could not determine update type - requires manual review"
          fi

      - name: Wait for CI to complete
        if: steps.check-update-type.outputs.safe_to_merge == 'true'
        uses: lewagon/wait-on-check-action@v1.3.4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          check-name: 'build-and-test'
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          wait-interval: 10
          allowed-conclusions: success

      - name: Enable auto-merge
        if: steps.check-update-type.outputs.safe_to_merge == 'true'
        run: |
          gh pr merge --auto --merge "${{ github.event.pull_request.number }}"
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Comment on PR
        if: steps.check-update-type.outputs.safe_to_merge == 'true'
        run: |
          gh pr comment "${{ github.event.pull_request.number }}" --body "🤖 Auto-merging this patch/minor dependency update after CI passes."
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}